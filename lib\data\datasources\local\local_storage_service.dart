import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/question.dart';
import '../../../domain/entities/quiz_result.dart';
import '../../../domain/entities/quiz_settings.dart';

class LocalStorageService {
  final SharedPreferences _sharedPreferences;
  final Box _settingsBox;
  final Box _leaderboardBox;

  LocalStorageService({
    required SharedPreferences sharedPreferences,
    required Box settingsBox,
    required Box leaderboardBox,
  })  : _sharedPreferences = sharedPreferences,
        _settingsBox = settingsBox,
        _leaderboardBox = leaderboardBox;

  // App Settings
  Future<AppSettings> getAppSettings() async {
    try {
      final isDarkMode = _sharedPreferences.getBool(AppConstants.themeKey) ?? false;
      final languageCode = _sharedPreferences.getString(AppConstants.languageKey) ?? AppConstants.defaultLanguage;
      final soundEnabled = _sharedPreferences.getBool(AppConstants.soundEnabledKey) ?? true;
      final vibrationEnabled = _sharedPreferences.getBool(AppConstants.vibrationEnabledKey) ?? true;
      final notificationsEnabled = _sharedPreferences.getBool(AppConstants.notificationsEnabledKey) ?? true;

      return AppSettings(
        isDarkMode: isDarkMode,
        languageCode: languageCode,
        soundEnabled: soundEnabled,
        vibrationEnabled: vibrationEnabled,
        notificationsEnabled: notificationsEnabled,
      );
    } catch (e) {
      return AppSettings.defaultSettings;
    }
  }

  Future<void> saveAppSettings(AppSettings settings) async {
    await Future.wait([
      _sharedPreferences.setBool(AppConstants.themeKey, settings.isDarkMode),
      _sharedPreferences.setString(AppConstants.languageKey, settings.languageCode),
      _sharedPreferences.setBool(AppConstants.soundEnabledKey, settings.soundEnabled),
      _sharedPreferences.setBool(AppConstants.vibrationEnabledKey, settings.vibrationEnabled),
      _sharedPreferences.setBool(AppConstants.notificationsEnabledKey, settings.notificationsEnabled),
    ]);
  }

  // Quiz Settings
  Future<QuizSettings> getQuizSettings() async {
    try {
      final settingsMap = _settingsBox.get('quiz_settings', defaultValue: <String, dynamic>{});
      return QuizSettings.fromMap(Map<String, dynamic>.from(settingsMap));
    } catch (e) {
      return const QuizSettings(numberOfQuestions: AppConstants.defaultQuestionCount);
    }
  }

  Future<void> saveQuizSettings(QuizSettings settings) async {
    await _settingsBox.put('quiz_settings', settings.toMap());
  }

  // Leaderboard
  Future<void> saveQuizResult(QuizResult result) async {
    try {
      final key = '${result.category}_${result.difficulty}_${result.completedAt.millisecondsSinceEpoch}';
      final resultMap = _quizResultToMap(result);
      await _leaderboardBox.put(key, resultMap);
    } catch (e) {
      throw Exception('Failed to save quiz result: $e');
    }
  }

  Future<List<QuizResult>> getLeaderboard({
    String? category,
    String? difficulty,
    int? limit,
  }) async {
    try {
      final allResults = <QuizResult>[];

      for (final key in _leaderboardBox.keys) {
        final resultMap = _leaderboardBox.get(key);
        if (resultMap != null) {
          final result = _quizResultFromMap(Map<String, dynamic>.from(resultMap));

          // Apply filters
          bool matches = true;
          if (category != null && result.category != category) matches = false;
          if (difficulty != null && result.difficulty != difficulty) matches = false;

          if (matches) {
            allResults.add(result);
          }
        }
      }

      // Sort by score (descending) and then by completion time (ascending)
      allResults.sort((a, b) {
        final scoreComparison = b.totalScore.compareTo(a.totalScore);
        if (scoreComparison != 0) return scoreComparison;
        return a.completedAt.compareTo(b.completedAt);
      });

      // Apply limit
      if (limit != null && limit > 0) {
        return allResults.take(limit).toList();
      }

      return allResults;
    } catch (e) {
      return [];
    }
  }

  Future<void> clearLeaderboard() async {
    await _leaderboardBox.clear();
  }

  Map<String, dynamic> _quizResultToMap(QuizResult result) {
    return {
      'questions': result.questions.map((q) => {
        'category': q.category,
        'type': q.type,
        'difficulty': q.difficulty,
        'question': q.question,
        'correctAnswer': q.correctAnswer,
        'incorrectAnswers': q.incorrectAnswers,
        'allAnswers': q.allAnswers,
      }).toList(),
      'userAnswers': result.userAnswers,
      'score': result.score,
      'totalQuestions': result.totalQuestions,
      'category': result.category,
      'difficulty': result.difficulty,
      'completedAt': result.completedAt.millisecondsSinceEpoch,
      'timeTaken': result.timeTaken.inMilliseconds,
    };
  }

  QuizResult _quizResultFromMap(Map<String, dynamic> map) {
    final questions = (map['questions'] as List<dynamic>).map((q) {
      final questionMap = Map<String, dynamic>.from(q);
      return Question(
        category: questionMap['category'] ?? '',
        type: questionMap['type'] ?? 'multiple',
        difficulty: questionMap['difficulty'] ?? 'medium',
        question: questionMap['question'] ?? '',
        correctAnswer: questionMap['correctAnswer'] ?? '',
        incorrectAnswers: List<String>.from(questionMap['incorrectAnswers'] ?? []),
        allAnswers: List<String>.from(questionMap['allAnswers'] ?? []),
      );
    }).toList();

    return QuizResult(
      questions: questions,
      userAnswers: List<String>.from(map['userAnswers'] ?? []),
      score: map['score'] ?? 0,
      totalQuestions: map['totalQuestions'] ?? 0,
      category: map['category'] ?? '',
      difficulty: map['difficulty'] ?? '',
      completedAt: DateTime.fromMillisecondsSinceEpoch(map['completedAt'] ?? 0),
      timeTaken: Duration(milliseconds: map['timeTaken'] ?? 0),
    );
  }
}
