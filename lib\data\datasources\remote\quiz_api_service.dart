import 'package:dio/dio.dart';
import '../../../core/constants/app_constants.dart';
import '../../models/category_model.dart';
import '../../models/question_model.dart';

class QuizApiService {
  final Dio _dio;

  QuizApiService(this._dio);

  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await _dio.get(ApiEndpoints.categories);
      
      if (response.statusCode == 200) {
        final data = response.data;
        final categories = (data['trivia_categories'] as List<dynamic>?)
                ?.map((category) => CategoryModel.fromJson(category))
                .toList() ??
            [];
        
        return categories;
      } else {
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  Future<List<QuestionModel>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    try {
      final url = ApiEndpoints.getQuestions(
        amount: amount,
        category: category,
        difficulty: difficulty,
        type: type,
      );
      
      final response = await _dio.get(url);
      
      if (response.statusCode == 200) {
        final data = response.data;
        
        // Check response code from OpenTDB
        final responseCode = data['response_code'] as int?;
        if (responseCode != 0) {
          throw Exception(_getErrorMessage(responseCode));
        }
        
        final questions = (data['results'] as List<dynamic>?)
                ?.map((question) => QuestionModel.fromJson(question))
                .toList() ??
            [];
        
        if (questions.isEmpty) {
          throw Exception('No questions found for the selected criteria');
        }
        
        return questions;
      } else {
        throw Exception('Failed to load questions: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  String _getErrorMessage(int? responseCode) {
    switch (responseCode) {
      case 1:
        return 'No results found. Try different parameters.';
      case 2:
        return 'Invalid parameter. Please check your settings.';
      case 3:
        return 'Token not found. Please try again.';
      case 4:
        return 'Token empty. Please try again.';
      case 5:
        return 'Rate limit exceeded. Please wait before trying again.';
      default:
        return 'Unknown error occurred. Please try again.';
    }
  }
}
