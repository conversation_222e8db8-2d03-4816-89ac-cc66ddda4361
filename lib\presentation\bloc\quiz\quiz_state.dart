import 'package:equatable/equatable.dart';
import '../../../domain/entities/category.dart';
import '../../../domain/entities/question.dart';
import '../../../domain/entities/quiz_result.dart';
import '../../../domain/entities/quiz_settings.dart';

abstract class QuizState extends Equatable {
  const QuizState();

  @override
  List<Object?> get props => [];
}

class QuizInitial extends QuizState {}

class QuizLoading extends QuizState {}

class CategoriesLoaded extends QuizState {
  final List<Category> categories;

  const CategoriesLoaded(this.categories);

  @override
  List<Object?> get props => [categories];
}

class QuestionsLoaded extends QuizState {
  final List<Question> questions;
  final QuizSettings settings;

  const QuestionsLoaded(this.questions, this.settings);

  @override
  List<Object?> get props => [questions, settings];
}

class QuizInProgress extends QuizState {
  final List<Question> questions;
  final int currentQuestionIndex;
  final List<String> userAnswers;
  final String? selectedAnswer;
  final bool isAnswered;
  final DateTime startTime;
  final QuizSettings settings;

  const QuizInProgress({
    required this.questions,
    required this.currentQuestionIndex,
    required this.userAnswers,
    this.selectedAnswer,
    required this.isAnswered,
    required this.startTime,
    required this.settings,
  });

  @override
  List<Object?> get props => [
        questions,
        currentQuestionIndex,
        userAnswers,
        selectedAnswer,
        isAnswered,
        startTime,
        settings,
      ];

  Question get currentQuestion => questions[currentQuestionIndex];
  
  bool get isLastQuestion => currentQuestionIndex >= questions.length - 1;
  
  int get progress => currentQuestionIndex + 1;
  
  double get progressPercentage => progress / questions.length;

  QuizInProgress copyWith({
    List<Question>? questions,
    int? currentQuestionIndex,
    List<String>? userAnswers,
    String? selectedAnswer,
    bool? isAnswered,
    DateTime? startTime,
    QuizSettings? settings,
  }) {
    return QuizInProgress(
      questions: questions ?? this.questions,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      userAnswers: userAnswers ?? this.userAnswers,
      selectedAnswer: selectedAnswer ?? this.selectedAnswer,
      isAnswered: isAnswered ?? this.isAnswered,
      startTime: startTime ?? this.startTime,
      settings: settings ?? this.settings,
    );
  }
}

class QuizCompleted extends QuizState {
  final QuizResult result;

  const QuizCompleted(this.result);

  @override
  List<Object?> get props => [result];
}

class QuizError extends QuizState {
  final String message;

  const QuizError(this.message);

  @override
  List<Object?> get props => [message];
}
