import '../../domain/entities/quiz_settings.dart';
import '../../domain/repositories/settings_repository.dart';
import '../datasources/local/local_storage_service.dart';

class SettingsRepositoryImpl implements SettingsRepository {
  final LocalStorageService _localStorageService;

  SettingsRepositoryImpl({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;

  @override
  Future<AppSettings> getAppSettings() async {
    try {
      return await _localStorageService.getAppSettings();
    } catch (e) {
      throw Exception('Failed to get app settings: $e');
    }
  }

  @override
  Future<void> updateAppSettings(AppSettings settings) async {
    try {
      await _localStorageService.saveAppSettings(settings);
    } catch (e) {
      throw Exception('Failed to update app settings: $e');
    }
  }

  @override
  Future<QuizSettings> getQuizSettings() async {
    try {
      return await _localStorageService.getQuizSettings();
    } catch (e) {
      throw Exception('Failed to get quiz settings: $e');
    }
  }

  @override
  Future<void> updateQuizSettings(QuizSettings settings) async {
    try {
      await _localStorageService.saveQuizSettings(settings);
    } catch (e) {
      throw Exception('Failed to update quiz settings: $e');
    }
  }
}
