class AppConstants {
  // API Configuration
  static const String baseUrl = 'https://opentdb.com/api.php';
  static const String categoriesUrl = 'https://opentdb.com/api_category.php';
  
  // App Configuration
  static const String appName = 'Quiz Master';
  static const String appVersion = '1.0.0';
  
  // Quiz Configuration
  static const int defaultQuestionCount = 10;
  static const int maxQuestionCount = 50;
  static const int minQuestionCount = 5;
  static const int questionTimeLimit = 30; // seconds
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 800);
  
  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String soundEnabledKey = 'sound_enabled';
  static const String vibrationEnabledKey = 'vibration_enabled';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String leaderboardKey = 'leaderboard_data';
  
  // Notification Configuration
  static const String notificationChannelId = 'quiz_master_channel';
  static const String notificationChannelName = 'Quiz Master Notifications';
  static const String notificationChannelDescription = 'Daily quiz reminders and updates';
  
  // Score Thresholds
  static const double excellentScoreThreshold = 0.9;
  static const double goodScoreThreshold = 0.7;
  static const double averageScoreThreshold = 0.5;
  
  // Supported Languages
  static const List<String> supportedLanguages = ['en', 'fr', 'ar'];
  
  // Default Values
  static const String defaultLanguage = 'en';
  static const String defaultDifficulty = 'medium';
  static const int defaultCategoryId = 9; // General Knowledge
}

class ApiEndpoints {
  static String getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type = 'multiple',
  }) {
    final params = <String, String>{
      'amount': amount.toString(),
      'type': type ?? 'multiple',
    };
    
    if (category != null) params['category'] = category.toString();
    if (difficulty != null) params['difficulty'] = difficulty;
    
    final queryString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    
    return '${AppConstants.baseUrl}?$queryString';
  }
  
  static String get categories => AppConstants.categoriesUrl;
}

class AssetPaths {
  // Images
  static const String logoPath = 'assets/images/logo.png';
  static const String mascotPath = 'assets/images/mascot.png';
  static const String backgroundPath = 'assets/images/background.png';
  
  // Animations
  static const String loadingAnimation = 'assets/animations/loading.json';
  static const String successAnimation = 'assets/animations/success.json';
  static const String confettiAnimation = 'assets/animations/confetti.json';
  static const String mascotAnimation = 'assets/animations/mascot.json';
  
  // Sounds
  static const String clickSound = 'assets/sounds/click.mp3';
  static const String correctSound = 'assets/sounds/correct.mp3';
  static const String incorrectSound = 'assets/sounds/incorrect.mp3';
  static const String completionSound = 'assets/sounds/completion.mp3';
}
