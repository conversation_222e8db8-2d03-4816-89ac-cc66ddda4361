import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:vibration/vibration.dart';
import 'package:audioplayers/audioplayers.dart';

import '../../core/constants/app_constants.dart';
import '../bloc/quiz/quiz_bloc.dart';
import '../bloc/quiz/quiz_event.dart';
import '../bloc/quiz/quiz_state.dart';
import '../bloc/settings/settings_bloc.dart';
import '../bloc/settings/settings_state.dart';
import '../widgets/animated_progress_bar.dart';
import '../widgets/answer_button.dart';
import '../widgets/question_card.dart';
import '../widgets/quiz_timer.dart';
import 'results_screen.dart';

class QuizScreen extends StatefulWidget {
  const QuizScreen({super.key});

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  Timer? _questionTimer;
  int _timeRemaining = AppConstants.questionTimeLimit;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _slideController.forward();
    _fadeController.forward();
    _scaleController.forward();
  }

  void _startQuestionTimer() {
    _timeRemaining = AppConstants.questionTimeLimit;
    _questionTimer?.cancel();
    _questionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _timeRemaining--;
        });

        if (_timeRemaining <= 0) {
          timer.cancel();
          _handleTimeUp();
        }
      }
    });
  }

  void _handleTimeUp() {
    final quizBloc = context.read<QuizBloc>();
    if (quizBloc.state is QuizInProgress) {
      final state = quizBloc.state as QuizInProgress;
      if (!state.isAnswered) {
        quizBloc.add(const AnswerQuestion(''));
        _playSound('incorrect');
        _triggerVibration();
      }
    }
  }

  void _onAnswerSelected(String answer) {
    final quizBloc = context.read<QuizBloc>();
    final state = quizBloc.state as QuizInProgress;

    if (!state.isAnswered) {
      quizBloc.add(AnswerQuestion(answer));
      _questionTimer?.cancel();

      final isCorrect = state.currentQuestion.isCorrectAnswer(answer);
      _playSound(isCorrect ? 'correct' : 'incorrect');
      _triggerVibration();

      // Auto-advance after showing feedback
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          quizBloc.add(NextQuestion());
          _animateToNextQuestion();
        }
      });
    }
  }

  void _animateToNextQuestion() {
    _slideController.reset();
    _fadeController.reset();
    _slideController.forward();
    _fadeController.forward();
    _startQuestionTimer();
  }

  void _playSound(String soundType) {
    final settingsState = context.read<SettingsBloc>().state;
    if (settingsState is SettingsLoaded && settingsState.settings.soundEnabled) {
      String soundPath;
      switch (soundType) {
        case 'correct':
          soundPath = AssetPaths.correctSound;
          break;
        case 'incorrect':
          soundPath = AssetPaths.incorrectSound;
          break;
        default:
          soundPath = AssetPaths.clickSound;
      }
      _audioPlayer.play(AssetSource(soundPath));
    }
  }

  void _triggerVibration() {
    final settingsState = context.read<SettingsBloc>().state;
    if (settingsState is SettingsLoaded && settingsState.settings.vibrationEnabled) {
      Vibration.vibrate(duration: 100);
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _questionTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        title: const Text('Quiz Master'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: BlocConsumer<QuizBloc, QuizState>(
        listener: (context, state) {
          if (state is QuizInProgress && _questionTimer == null) {
            _startQuestionTimer();
          } else if (state is QuizCompleted) {
            _questionTimer?.cancel();
            Navigator.of(context).pushReplacement(
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    const ResultsScreen(),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  return FadeTransition(opacity: animation, child: child);
                },
                transitionDuration: AppConstants.mediumAnimation,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is QuizLoading) {
            return _buildLoadingWidget();
          } else if (state is QuizInProgress) {
            return _buildQuizContent(state);
          } else if (state is QuizError) {
            return _buildErrorWidget(state.message);
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.asset(
            AssetPaths.loadingAnimation,
            width: 150,
            height: 150,
          ),
          const SizedBox(height: 24),
          Text(
            'Loading Questions...',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizContent(QuizInProgress state) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Progress and Timer Row
            Row(
              children: [
                Expanded(
                  child: AnimatedProgressBar(
                    progress: state.progressPercentage,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 16),
                QuizTimer(
                  timeRemaining: _timeRemaining,
                  totalTime: AppConstants.questionTimeLimit,
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Question Counter
            Text(
              'Question ${state.progress} of ${state.questions.length}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // Question Card
            Expanded(
              flex: 2,
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: QuestionCard(
                    question: state.currentQuestion,
                    questionNumber: state.progress,
                  ),
                ),
              ),
            ),

            // Answer Buttons
            Expanded(
              flex: 2,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: _buildAnswerButtons(state),
              ),
            ),

            // Mascot Animation (bottom corner)
            Align(
              alignment: Alignment.bottomRight,
              child: SizedBox(
                width: 80,
                height: 80,
                child: Lottie.asset(
                  AssetPaths.mascotAnimation,
                  repeat: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnswerButtons(QuizInProgress state) {
    return ListView.builder(
      itemCount: state.currentQuestion.allAnswers.length,
      itemBuilder: (context, index) {
        final answer = state.currentQuestion.allAnswers[index];
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: AnswerButton(
            answer: answer,
            isSelected: state.selectedAnswer == answer,
            isCorrect: state.isAnswered &&
                      state.currentQuestion.isCorrectAnswer(answer),
            isIncorrect: state.isAnswered &&
                        state.selectedAnswer == answer &&
                        !state.currentQuestion.isCorrectAnswer(answer),
            onPressed: state.isAnswered ? null : () => _onAnswerSelected(answer),
          ),
        );
      },
    );
  }
}
