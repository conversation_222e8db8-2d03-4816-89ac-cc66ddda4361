import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/quiz_result.dart';
import '../../../domain/usecases/get_categories.dart';
import '../../../domain/usecases/get_questions.dart';
import '../../../domain/usecases/save_score.dart';
import 'quiz_event.dart';
import 'quiz_state.dart';

class QuizBloc extends Bloc<QuizEvent, QuizState> {
  final GetCategories _getCategories;
  final GetQuestions _getQuestions;
  final SaveScore _saveScore;

  QuizBloc({
    required GetCategories getCategories,
    required GetQuestions getQuestions,
    required SaveScore saveScore,
  })  : _getCategories = getCategories,
        _getQuestions = getQuestions,
        _saveScore = saveScore,
        super(QuizInitial()) {
    on<LoadCategories>(_onLoadCategories);
    on<LoadQuestions>(_onLoadQuestions);
    on<AnswerQuestion>(_onAnswerQuestion);
    on<NextQuestion>(_onNextQuestion);
    on<FinishQuiz>(_onFinishQuiz);
    on<ResetQuiz>(_onResetQuiz);
  }

  Future<void> _onLoadCategories(
    LoadCategories event,
    Emitter<QuizState> emit,
  ) async {
    emit(QuizLoading());
    try {
      final categories = await _getCategories();
      emit(CategoriesLoaded(categories));
    } catch (e) {
      emit(QuizError(e.toString()));
    }
  }

  Future<void> _onLoadQuestions(
    LoadQuestions event,
    Emitter<QuizState> emit,
  ) async {
    emit(QuizLoading());
    try {
      final questions = await _getQuestions(event.settings);
      if (questions.isEmpty) {
        emit(const QuizError('No questions found for the selected criteria'));
        return;
      }
      
      emit(QuizInProgress(
        questions: questions,
        currentQuestionIndex: 0,
        userAnswers: [],
        isAnswered: false,
        startTime: DateTime.now(),
        settings: event.settings,
      ));
    } catch (e) {
      emit(QuizError(e.toString()));
    }
  }

  void _onAnswerQuestion(
    AnswerQuestion event,
    Emitter<QuizState> emit,
  ) {
    if (state is QuizInProgress) {
      final currentState = state as QuizInProgress;
      
      if (!currentState.isAnswered) {
        emit(currentState.copyWith(
          selectedAnswer: event.answer,
          isAnswered: true,
        ));
      }
    }
  }

  void _onNextQuestion(
    NextQuestion event,
    Emitter<QuizState> emit,
  ) {
    if (state is QuizInProgress) {
      final currentState = state as QuizInProgress;
      
      if (currentState.isAnswered) {
        final updatedAnswers = List<String>.from(currentState.userAnswers)
          ..add(currentState.selectedAnswer ?? '');
        
        if (currentState.isLastQuestion) {
          add(FinishQuiz());
        } else {
          emit(currentState.copyWith(
            currentQuestionIndex: currentState.currentQuestionIndex + 1,
            userAnswers: updatedAnswers,
            selectedAnswer: null,
            isAnswered: false,
          ));
        }
      }
    }
  }

  Future<void> _onFinishQuiz(
    FinishQuiz event,
    Emitter<QuizState> emit,
  ) async {
    if (state is QuizInProgress) {
      final currentState = state as QuizInProgress;
      
      // Add the last answer if it exists
      final finalAnswers = List<String>.from(currentState.userAnswers);
      if (currentState.selectedAnswer != null) {
        finalAnswers.add(currentState.selectedAnswer!);
      }
      
      // Calculate score
      int score = 0;
      for (int i = 0; i < currentState.questions.length && i < finalAnswers.length; i++) {
        if (currentState.questions[i].isCorrectAnswer(finalAnswers[i])) {
          score++;
        }
      }
      
      final result = QuizResult(
        questions: currentState.questions,
        userAnswers: finalAnswers,
        score: score,
        totalQuestions: currentState.questions.length,
        category: currentState.settings.categoryId?.toString() ?? 'Any',
        difficulty: currentState.settings.difficulty ?? 'Any',
        completedAt: DateTime.now(),
        timeTaken: DateTime.now().difference(currentState.startTime),
      );
      
      try {
        await _saveScore(result);
        emit(QuizCompleted(result));
      } catch (e) {
        // Still show the result even if saving fails
        emit(QuizCompleted(result));
      }
    }
  }

  void _onResetQuiz(
    ResetQuiz event,
    Emitter<QuizState> emit,
  ) {
    emit(QuizInitial());
  }
}
