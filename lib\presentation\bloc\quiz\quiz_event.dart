import 'package:equatable/equatable.dart';
import '../../../domain/entities/category.dart';
import '../../../domain/entities/quiz_settings.dart';

abstract class QuizEvent extends Equatable {
  const QuizEvent();

  @override
  List<Object?> get props => [];
}

class LoadCategories extends QuizEvent {}

class LoadQuestions extends QuizEvent {
  final QuizSettings settings;

  const LoadQuestions(this.settings);

  @override
  List<Object?> get props => [settings];
}

class AnswerQuestion extends QuizEvent {
  final String answer;

  const AnswerQuestion(this.answer);

  @override
  List<Object?> get props => [answer];
}

class NextQuestion extends QuizEvent {}

class FinishQuiz extends QuizEvent {}

class ResetQuiz extends QuizEvent {}

class UpdateQuizSettings extends QuizEvent {
  final QuizSettings settings;

  const UpdateQuizSettings(this.settings);

  @override
  List<Object?> get props => [settings];
}
