import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../data/datasources/local/local_storage_service.dart';
import '../../data/datasources/remote/quiz_api_service.dart';
import '../../data/repositories/quiz_repository_impl.dart';
import '../../data/repositories/settings_repository_impl.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../domain/usecases/get_categories.dart';
import '../../domain/usecases/get_questions.dart';
import '../../domain/usecases/save_score.dart';
import '../../domain/usecases/get_leaderboard.dart';
import '../../domain/usecases/get_settings.dart';
import '../../domain/usecases/update_settings.dart';
import '../../presentation/bloc/quiz/quiz_bloc.dart';
import '../../presentation/bloc/settings/settings_bloc.dart';
import '../../presentation/bloc/leaderboard/leaderboard_bloc.dart';

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async {
  // Initialize Hive
  await Hive.initFlutter();
  
  // Register SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);
  
  // Register Dio
  final dio = Dio();
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  getIt.registerSingleton<Dio>(dio);
  
  // Register Hive boxes
  final settingsBox = await Hive.openBox('settings');
  final leaderboardBox = await Hive.openBox('leaderboard');
  getIt.registerSingleton<Box>(settingsBox, instanceName: 'settings');
  getIt.registerSingleton<Box>(leaderboardBox, instanceName: 'leaderboard');
  
  // Data Sources
  getIt.registerLazySingleton<LocalStorageService>(
    () => LocalStorageService(
      sharedPreferences: getIt<SharedPreferences>(),
      settingsBox: getIt<Box>(instanceName: 'settings'),
      leaderboardBox: getIt<Box>(instanceName: 'leaderboard'),
    ),
  );
  
  getIt.registerLazySingleton<QuizApiService>(
    () => QuizApiService(getIt<Dio>()),
  );
  
  // Repositories
  getIt.registerLazySingleton<QuizRepository>(
    () => QuizRepositoryImpl(
      apiService: getIt<QuizApiService>(),
      localStorageService: getIt<LocalStorageService>(),
    ),
  );
  
  getIt.registerLazySingleton<SettingsRepository>(
    () => SettingsRepositoryImpl(
      localStorageService: getIt<LocalStorageService>(),
    ),
  );
  
  // Use Cases
  getIt.registerLazySingleton<GetCategories>(
    () => GetCategories(getIt<QuizRepository>()),
  );
  
  getIt.registerLazySingleton<GetQuestions>(
    () => GetQuestions(getIt<QuizRepository>()),
  );
  
  getIt.registerLazySingleton<SaveScore>(
    () => SaveScore(getIt<QuizRepository>()),
  );
  
  getIt.registerLazySingleton<GetLeaderboard>(
    () => GetLeaderboard(getIt<QuizRepository>()),
  );
  
  getIt.registerLazySingleton<GetSettings>(
    () => GetSettings(getIt<SettingsRepository>()),
  );
  
  getIt.registerLazySingleton<UpdateSettings>(
    () => UpdateSettings(getIt<SettingsRepository>()),
  );
  
  // BLoCs
  getIt.registerFactory<QuizBloc>(
    () => QuizBloc(
      getCategories: getIt<GetCategories>(),
      getQuestions: getIt<GetQuestions>(),
      saveScore: getIt<SaveScore>(),
    ),
  );
  
  getIt.registerFactory<SettingsBloc>(
    () => SettingsBloc(
      getSettings: getIt<GetSettings>(),
      updateSettings: getIt<UpdateSettings>(),
    ),
  );
  
  getIt.registerFactory<LeaderboardBloc>(
    () => LeaderboardBloc(
      getLeaderboard: getIt<GetLeaderboard>(),
    ),
  );
}

// Helper function to reset dependencies for testing
Future<void> resetDependencies() async {
  await getIt.reset();
  await configureDependencies();
}
