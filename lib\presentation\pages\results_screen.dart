import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:audioplayers/audioplayers.dart';

import '../../core/constants/app_constants.dart';
import '../../core/themes/app_theme.dart';
import '../bloc/quiz/quiz_bloc.dart';
import '../bloc/quiz/quiz_event.dart';
import '../bloc/quiz/quiz_state.dart';
import '../bloc/settings/settings_bloc.dart';
import '../bloc/settings/settings_state.dart';
import '../widgets/animated_score_counter.dart';
import '../widgets/performance_indicator.dart';
import '../widgets/question_review_list.dart';
import '../widgets/animated_button.dart';

class ResultsScreen extends StatefulWidget {
  const ResultsScreen({super.key});

  @override
  State<ResultsScreen> createState() => _ResultsScreenState();
}

class _ResultsScreenState extends State<ResultsScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _confettiController;
  late AnimationController _scoreController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _showConfetti = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkForCelebration();
  }

  void _initializeAnimations() {
    _mainController = AnimationController(
      duration: AppConstants.longAnimation,
      vsync: this,
    );
    _confettiController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _scoreController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.4, 1.0, curve: Curves.elasticOut),
    ));

    _mainController.forward();

    // Delay score animation
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _scoreController.forward();
      }
    });
  }

  void _checkForCelebration() {
    final quizState = context.read<QuizBloc>().state;
    if (quizState is QuizCompleted) {
      final percentage = quizState.result.percentage;
      if (percentage >= AppConstants.excellentScoreThreshold * 100) {
        setState(() {
          _showConfetti = true;
        });
        _confettiController.forward();
        _playCompletionSound();
      }
    }
  }

  void _playCompletionSound() {
    final settingsState = context.read<SettingsBloc>().state;
    if (settingsState is SettingsLoaded && settingsState.settings.soundEnabled) {
      _audioPlayer.play(AssetSource(AssetPaths.completionSound));
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _confettiController.dispose();
    _scoreController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: BlocBuilder<QuizBloc, QuizState>(
        builder: (context, state) {
          if (state is QuizCompleted) {
            return _buildResultsContent(state.result);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildResultsContent(result) {
    return SafeArea(
      child: Stack(
        children: [
          // Main Content
          FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  const SizedBox(height: 40),

                  // Header
                  SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      children: [
                        Icon(
                          _getResultIcon(result.percentage),
                          size: 80,
                          color: _getResultColor(result.percentage),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _getResultTitle(result.percentage),
                          style: Theme.of(context).textTheme.displaySmall?.copyWith(
                            color: _getResultColor(result.percentage),
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getResultSubtitle(result.percentage),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Score Section
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          AnimatedScoreCounter(
                            score: result.score,
                            totalQuestions: result.totalQuestions,
                            animationController: _scoreController,
                          ),
                          const SizedBox(height: 16),
                          PerformanceIndicator(
                            percentage: result.percentage,
                            performanceLevel: result.performanceLevel,
                          ),
                          const SizedBox(height: 16),
                          _buildStatsRow(result),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Question Review
                  SlideTransition(
                    position: _slideAnimation,
                    child: QuestionReviewList(
                      questionResults: result.questionResults,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Action Buttons
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Column(
                      children: [
                        AnimatedButton(
                          text: 'Play Again',
                          icon: Icons.refresh,
                          onPressed: () => _playAgain(),
                          backgroundColor: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        AnimatedButton(
                          text: 'Back to Home',
                          icon: Icons.home,
                          onPressed: () => _goHome(),
                          backgroundColor: Theme.of(context).colorScheme.secondary,
                        ),
                        const SizedBox(height: 16),
                        AnimatedButton(
                          text: 'View Leaderboard',
                          icon: Icons.leaderboard,
                          onPressed: () => _viewLeaderboard(),
                          backgroundColor: Theme.of(context).colorScheme.tertiary,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),

          // Confetti Animation
          if (_showConfetti)
            Positioned.fill(
              child: IgnorePointer(
                child: Lottie.asset(
                  AssetPaths.confettiAnimation,
                  controller: _confettiController,
                  fit: BoxFit.cover,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatsRow(result) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStatItem(
          'Time',
          _formatDuration(result.timeTaken),
          Icons.timer,
        ),
        _buildStatItem(
          'Category',
          result.category,
          Icons.category,
        ),
        _buildStatItem(
          'Difficulty',
          result.difficulty.toUpperCase(),
          Icons.trending_up,
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onBackground.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  IconData _getResultIcon(double percentage) {
    if (percentage >= 90) return Icons.emoji_events;
    if (percentage >= 70) return Icons.thumb_up;
    if (percentage >= 50) return Icons.sentiment_satisfied;
    return Icons.sentiment_dissatisfied;
  }

  Color _getResultColor(double percentage) {
    if (percentage >= 90) return Colors.amber;
    if (percentage >= 70) return AppTheme.correctAnswer;
    if (percentage >= 50) return AppTheme.warningColor;
    return AppTheme.incorrectAnswer;
  }

  String _getResultTitle(double percentage) {
    if (percentage >= 90) return 'Excellent!';
    if (percentage >= 70) return 'Well Done!';
    if (percentage >= 50) return 'Good Job!';
    return 'Keep Trying!';
  }

  String _getResultSubtitle(double percentage) {
    if (percentage >= 90) return 'You\'re a quiz master!';
    if (percentage >= 70) return 'Great performance!';
    if (percentage >= 50) return 'You\'re getting there!';
    return 'Practice makes perfect!';
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }

  void _playAgain() {
    context.read<QuizBloc>().add(ResetQuiz());
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void _goHome() {
    context.read<QuizBloc>().add(ResetQuiz());
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void _viewLeaderboard() {
    // Navigate to leaderboard screen
    // This would be implemented based on your navigation structure
  }
}
