import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/get_settings.dart';
import '../../../domain/usecases/update_settings.dart';
import 'settings_event.dart';
import 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final GetSettings _getSettings;
  final UpdateSettings _updateSettings;

  SettingsBloc({
    required GetSettings getSettings,
    required UpdateSettings updateSettings,
  })  : _getSettings = getSettings,
        _updateSettings = updateSettings,
        super(SettingsInitial()) {
    on<LoadSettings>(_onLoadSettings);
    on<UpdateTheme>(_onUpdateTheme);
    on<UpdateLanguage>(_onUpdateLanguage);
    on<UpdateSound>(_onUpdateSound);
    on<UpdateVibration>(_onUpdateVibration);
    on<UpdateNotifications>(_onUpdateNotifications);
    on<UpdateAppSettings>(_onUpdateAppSettings);
  }

  Future<void> _onLoadSettings(
    LoadSettings event,
    Emitter<SettingsState> emit,
  ) async {
    emit(SettingsLoading());
    try {
      final settings = await _getSettings();
      emit(SettingsLoaded(settings));
    } catch (e) {
      emit(SettingsError(e.toString()));
    }
  }

  Future<void> _onUpdateTheme(
    UpdateTheme event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentSettings = (state as SettingsLoaded).settings;
      final updatedSettings = currentSettings.copyWith(isDarkMode: event.isDarkMode);
      
      try {
        await _updateSettings(updatedSettings);
        emit(SettingsLoaded(updatedSettings));
      } catch (e) {
        emit(SettingsError(e.toString()));
      }
    }
  }

  Future<void> _onUpdateLanguage(
    UpdateLanguage event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentSettings = (state as SettingsLoaded).settings;
      final updatedSettings = currentSettings.copyWith(languageCode: event.languageCode);
      
      try {
        await _updateSettings(updatedSettings);
        emit(SettingsLoaded(updatedSettings));
      } catch (e) {
        emit(SettingsError(e.toString()));
      }
    }
  }

  Future<void> _onUpdateSound(
    UpdateSound event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentSettings = (state as SettingsLoaded).settings;
      final updatedSettings = currentSettings.copyWith(soundEnabled: event.soundEnabled);
      
      try {
        await _updateSettings(updatedSettings);
        emit(SettingsLoaded(updatedSettings));
      } catch (e) {
        emit(SettingsError(e.toString()));
      }
    }
  }

  Future<void> _onUpdateVibration(
    UpdateVibration event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentSettings = (state as SettingsLoaded).settings;
      final updatedSettings = currentSettings.copyWith(vibrationEnabled: event.vibrationEnabled);
      
      try {
        await _updateSettings(updatedSettings);
        emit(SettingsLoaded(updatedSettings));
      } catch (e) {
        emit(SettingsError(e.toString()));
      }
    }
  }

  Future<void> _onUpdateNotifications(
    UpdateNotifications event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentSettings = (state as SettingsLoaded).settings;
      final updatedSettings = currentSettings.copyWith(notificationsEnabled: event.notificationsEnabled);
      
      try {
        await _updateSettings(updatedSettings);
        emit(SettingsLoaded(updatedSettings));
      } catch (e) {
        emit(SettingsError(e.toString()));
      }
    }
  }

  Future<void> _onUpdateAppSettings(
    UpdateAppSettings event,
    Emitter<SettingsState> emit,
  ) async {
    try {
      await _updateSettings(event.settings);
      emit(SettingsLoaded(event.settings));
    } catch (e) {
      emit(SettingsError(e.toString()));
    }
  }
}
