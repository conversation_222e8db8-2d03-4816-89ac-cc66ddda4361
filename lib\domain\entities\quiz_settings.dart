import 'package:equatable/equatable.dart';

class QuizSettings extends Equatable {
  final int numberOfQuestions;
  final int? categoryId;
  final String? difficulty;
  final String type;

  const QuizSettings({
    required this.numberOfQuestions,
    this.categoryId,
    this.difficulty,
    this.type = 'multiple',
  });

  @override
  List<Object?> get props => [numberOfQuestions, categoryId, difficulty, type];

  QuizSettings copyWith({
    int? numberOfQuestions,
    int? categoryId,
    String? difficulty,
    String? type,
  }) {
    return QuizSettings(
      numberOfQuestions: numberOfQuestions ?? this.numberOfQuestions,
      categoryId: categoryId ?? this.categoryId,
      difficulty: difficulty ?? this.difficulty,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'numberOfQuestions': numberOfQuestions,
      'categoryId': categoryId,
      'difficulty': difficulty,
      'type': type,
    };
  }

  factory QuizSettings.fromMap(Map<String, dynamic> map) {
    return QuizSettings(
      numberOfQuestions: map['numberOfQuestions'] ?? 10,
      categoryId: map['categoryId'],
      difficulty: map['difficulty'],
      type: map['type'] ?? 'multiple',
    );
  }
}

class AppSettings extends Equatable {
  final bool isDarkMode;
  final String languageCode;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool notificationsEnabled;

  const AppSettings({
    required this.isDarkMode,
    required this.languageCode,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.notificationsEnabled,
  });

  @override
  List<Object?> get props => [
        isDarkMode,
        languageCode,
        soundEnabled,
        vibrationEnabled,
        notificationsEnabled,
      ];

  AppSettings copyWith({
    bool? isDarkMode,
    String? languageCode,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      languageCode: languageCode ?? this.languageCode,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'isDarkMode': isDarkMode,
      'languageCode': languageCode,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'notificationsEnabled': notificationsEnabled,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      isDarkMode: map['isDarkMode'] ?? false,
      languageCode: map['languageCode'] ?? 'en',
      soundEnabled: map['soundEnabled'] ?? true,
      vibrationEnabled: map['vibrationEnabled'] ?? true,
      notificationsEnabled: map['notificationsEnabled'] ?? true,
    );
  }

  static const AppSettings defaultSettings = AppSettings(
    isDarkMode: false,
    languageCode: 'en',
    soundEnabled: true,
    vibrationEnabled: true,
    notificationsEnabled: true,
  );
}
