import '../entities/quiz_result.dart';
import '../repositories/quiz_repository.dart';

class GetLeaderboard {
  final QuizRepository repository;

  GetLeaderboard(this.repository);

  Future<List<QuizResult>> call({
    String? category,
    String? difficulty,
    int? limit,
  }) async {
    return await repository.getLeaderboard(
      category: category,
      difficulty: difficulty,
      limit: limit,
    );
  }
}
