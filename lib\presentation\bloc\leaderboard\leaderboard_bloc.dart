import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/get_leaderboard.dart';
import 'leaderboard_event.dart';
import 'leaderboard_state.dart';

class LeaderboardBloc extends Bloc<LeaderboardEvent, LeaderboardState> {
  final GetLeaderboard _getLeaderboard;

  LeaderboardBloc({
    required GetLeaderboard getLeaderboard,
  })  : _getLeaderboard = getLeaderboard,
        super(LeaderboardInitial()) {
    on<LoadLeaderboard>(_onLoadLeaderboard);
    on<RefreshLeaderboard>(_onRefreshLeaderboard);
    on<FilterLeaderboard>(_onFilterLeaderboard);
  }

  Future<void> _onLoadLeaderboard(
    LoadLeaderboard event,
    Emitter<LeaderboardState> emit,
  ) async {
    emit(LeaderboardLoading());
    try {
      final results = await _getLeaderboard(
        category: event.category,
        difficulty: event.difficulty,
        limit: event.limit ?? 50,
      );
      emit(LeaderboardLoaded(
        results: results,
        categoryFilter: event.category,
        difficultyFilter: event.difficulty,
      ));
    } catch (e) {
      emit(LeaderboardError(e.toString()));
    }
  }

  Future<void> _onRefreshLeaderboard(
    RefreshLeaderboard event,
    Emitter<LeaderboardState> emit,
  ) async {
    if (state is LeaderboardLoaded) {
      final currentState = state as LeaderboardLoaded;
      add(LoadLeaderboard(
        category: currentState.categoryFilter,
        difficulty: currentState.difficultyFilter,
      ));
    } else {
      add(const LoadLeaderboard());
    }
  }

  Future<void> _onFilterLeaderboard(
    FilterLeaderboard event,
    Emitter<LeaderboardState> emit,
  ) async {
    add(LoadLeaderboard(
      category: event.category,
      difficulty: event.difficulty,
    ));
  }
}
