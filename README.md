# Quiz Master 🧠

A professional, ultra-modern mobile quiz application built with Flutter that fetches questions in real-time from the Open Trivia Database (OpenTDB) API.

## ✨ Features

### 🎯 Core Functionality
- **Real-time Question Fetching**: Dynamic questions from OpenTDB API
- **Multiple Categories**: Choose from various quiz categories
- **Difficulty Levels**: Easy, Medium, Hard, or Mixed
- **Customizable Quiz Length**: 5, 10, 15, 20, or 25 questions
- **Timer-based Questions**: 30-second countdown per question
- **Instant Feedback**: Immediate visual and audio feedback

### 🎨 Modern UI/UX
- **Material Design 3**: Latest design system implementation
- **Dark/Light Themes**: Seamless theme switching with animations
- **Smooth Animations**: Custom animations throughout the app
- **Responsive Design**: Optimized for all screen sizes
- **Google Fonts**: Beautiful typography with Poppins font family

### 🌍 Internationalization
- **Multi-language Support**: English, French, Arabic
- **RTL Support**: Full right-to-left layout support for Arabic
- **Easy Localization**: Powered by easy_localization package

### 🔊 Audio & Haptics
- **Sound Effects**: Click, correct, incorrect, and completion sounds
- **Vibration Feedback**: Haptic feedback for interactions
- **Configurable**: Toggle sound and vibration in settings

### 📊 Data & Analytics
- **Local Leaderboards**: Track your best scores
- **Performance Analytics**: Detailed quiz results and statistics
- **Progress Tracking**: Monitor improvement over time
- **Data Persistence**: Local storage with Hive database

### ⚙️ Advanced Features
- **State Management**: BLoC pattern for robust state handling
- **Dependency Injection**: Clean architecture with get_it
- **Error Handling**: Comprehensive error management
- **Offline Support**: Local data caching
- **Performance Optimized**: Efficient memory and battery usage

## 🏗️ Architecture

This project follows **Clean Architecture** principles with **MVVM/BLoC** pattern:

```
lib/
├── core/                   # Core utilities and constants
│   ├── constants/         # App constants and configurations
│   ├── themes/           # Theme definitions and styling
│   ├── di/              # Dependency injection setup
│   └── utils/           # Utility functions and helpers
├── data/                  # Data layer
│   ├── datasources/      # Local and remote data sources
│   ├── models/          # Data models and DTOs
│   └── repositories/    # Repository implementations
├── domain/               # Domain layer (Business Logic)
│   ├── entities/        # Business entities
│   ├── repositories/    # Repository interfaces
│   └── usecases/       # Business use cases
└── presentation/         # Presentation layer
    ├── bloc/           # BLoC state management
    ├── pages/          # Screen widgets
    └── widgets/        # Reusable UI components
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=3.8.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/quiz_master.git
   cd quiz_master
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code (if needed)**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Platform Setup

#### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: 34 (Android 14)
- Compile SDK: 34

#### iOS
- Minimum iOS version: 12.0
- Xcode 14.0 or later

## 📱 Screens Overview

### 🏠 Home Screen
- Welcome interface with app branding
- Animated entrance with fade and slide effects
- Quick access to quiz, leaderboard, and settings
- About dialog with app information

### ⚙️ Parameters Screen
- Dynamic category selection from OpenTDB
- Difficulty level picker with visual indicators
- Question count selector with chips
- Real-time preview of quiz settings
- Smooth transitions and animations

### 🎯 Quiz Screen
- Clean question presentation with category badges
- Animated progress bar and circular timer
- Interactive answer buttons with feedback
- Auto-advance with slide transitions
- Corner mascot animation (Lottie)

### 🏆 Results Screen
- Animated score counter with percentage
- Performance indicator with star rating
- Expandable question review list
- Action buttons with press animations
- Confetti animation for excellent scores

### ⚙️ Settings Screen
- Organized sections with icons
- Smooth toggle animations
- Language selection dialog
- Theme switching with immediate preview
- Audio and vibration controls

## 🎨 Design System

### Color Palette
- **Light Theme**: Clean whites and soft grays with vibrant accents
- **Dark Theme**: Rich dark backgrounds with bright highlights
- **Accent Colors**: Blue (#0057B7), Green (#28A745), Purple (#6F42C1)

### Typography
- **Primary Font**: Poppins (Google Fonts)
- **Weights**: Regular (400), Medium (500), SemiBold (600), Bold (700)
- **Responsive Sizing**: Scales based on device and accessibility settings

### Animations
- **Duration Standards**: Short (300ms), Medium (500ms), Long (800ms)
- **Curves**: Material motion curves for natural feel
- **Types**: Fade, Slide, Scale, Rotation, and custom animations

## 🔧 Configuration

### API Configuration
The app uses the Open Trivia Database API:
- **Base URL**: `https://opentdb.com/api.php`
- **Categories**: `https://opentdb.com/api_category.php`
- **Rate Limiting**: Respects API guidelines
- **Error Handling**: Comprehensive error management

### Local Storage
- **SharedPreferences**: User settings and preferences
- **Hive Database**: Quiz results and leaderboard data
- **Encryption**: Sensitive data protection (if needed)

## 🧪 Testing

### Running Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test/
```

### Test Coverage
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for user flows
- BLoC tests for state management

## 📦 Dependencies

### Core Dependencies
- `flutter_bloc`: State management
- `get_it`: Dependency injection
- `dio`: HTTP client
- `hive`: Local database
- `shared_preferences`: Simple storage

### UI Dependencies
- `google_fonts`: Typography
- `lottie`: Animations
- `fl_chart`: Charts and graphs
- `animated_text_kit`: Text animations

### Utility Dependencies
- `easy_localization`: Internationalization
- `html_unescape`: HTML entity decoding
- `vibration`: Haptic feedback
- `audioplayers`: Sound effects

## 🚀 Deployment

### Android
```bash
flutter build apk --release
# or
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Open Trivia Database**: For providing the quiz questions API
- **Flutter Team**: For the amazing framework
- **Material Design**: For the design system
- **Community**: For the open-source packages used

## 📞 Support

For support, email <EMAIL> or join our Discord server.

---

**Made with ❤️ using Flutter**
