import 'package:equatable/equatable.dart';
import '../../../domain/entities/quiz_result.dart';

abstract class LeaderboardState extends Equatable {
  const LeaderboardState();

  @override
  List<Object?> get props => [];
}

class LeaderboardInitial extends LeaderboardState {}

class LeaderboardLoading extends LeaderboardState {}

class LeaderboardLoaded extends LeaderboardState {
  final List<QuizResult> results;
  final String? categoryFilter;
  final String? difficultyFilter;

  const LeaderboardLoaded({
    required this.results,
    this.categoryFilter,
    this.difficultyFilter,
  });

  @override
  List<Object?> get props => [results, categoryFilter, difficultyFilter];
}

class LeaderboardError extends LeaderboardState {
  final String message;

  const LeaderboardError(this.message);

  @override
  List<Object?> get props => [message];
}
