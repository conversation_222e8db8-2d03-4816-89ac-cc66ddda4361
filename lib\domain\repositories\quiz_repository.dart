import '../entities/category.dart';
import '../entities/question.dart';
import '../entities/quiz_result.dart';
import '../entities/quiz_settings.dart';

abstract class QuizRepository {
  Future<List<Category>> getCategories();
  Future<List<Question>> getQuestions(QuizSettings settings);
  Future<void> saveQuizResult(QuizResult result);
  Future<List<QuizResult>> getLeaderboard({
    String? category,
    String? difficulty,
    int? limit,
  });
  Future<void> clearLeaderboard();
}
