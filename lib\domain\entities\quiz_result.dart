import 'package:equatable/equatable.dart';
import 'question.dart';

class QuizR<PERSON>ult extends Equatable {
  final List<Question> questions;
  final List<String> userAnswers;
  final int score;
  final int totalQuestions;
  final String category;
  final String difficulty;
  final DateTime completedAt;
  final Duration timeTaken;

  const QuizResult({
    required this.questions,
    required this.userAnswers,
    required this.score,
    required this.totalQuestions,
    required this.category,
    required this.difficulty,
    required this.completedAt,
    required this.timeTaken,
  });

  @override
  List<Object?> get props => [
        questions,
        userAnswers,
        score,
        totalQuestions,
        category,
        difficulty,
        completedAt,
        timeTaken,
      ];

  double get percentage => (score / totalQuestions) * 100;

  String get performanceLevel {
    final percent = percentage;
    if (percent >= 90) return 'Excellent';
    if (percent >= 70) return 'Good';
    if (percent >= 50) return 'Average';
    return 'Needs Improvement';
  }

  int get totalScore {
    int total = 0;
    for (int i = 0; i < questions.length; i++) {
      if (i < userAnswers.length && 
          questions[i].isCorrectAnswer(userAnswers[i])) {
        total += questions[i].difficultyScore;
      }
    }
    return total;
  }

  List<QuestionResult> get questionResults {
    final results = <QuestionResult>[];
    for (int i = 0; i < questions.length; i++) {
      final userAnswer = i < userAnswers.length ? userAnswers[i] : '';
      results.add(QuestionResult(
        question: questions[i],
        userAnswer: userAnswer,
        isCorrect: questions[i].isCorrectAnswer(userAnswer),
      ));
    }
    return results;
  }

  QuizResult copyWith({
    List<Question>? questions,
    List<String>? userAnswers,
    int? score,
    int? totalQuestions,
    String? category,
    String? difficulty,
    DateTime? completedAt,
    Duration? timeTaken,
  }) {
    return QuizResult(
      questions: questions ?? this.questions,
      userAnswers: userAnswers ?? this.userAnswers,
      score: score ?? this.score,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      completedAt: completedAt ?? this.completedAt,
      timeTaken: timeTaken ?? this.timeTaken,
    );
  }
}

class QuestionResult extends Equatable {
  final Question question;
  final String userAnswer;
  final bool isCorrect;

  const QuestionResult({
    required this.question,
    required this.userAnswer,
    required this.isCorrect,
  });

  @override
  List<Object?> get props => [question, userAnswer, isCorrect];
}
