import 'package:flutter/material.dart';
import '../../core/themes/app_theme.dart';

class AnswerButton extends StatefulWidget {
  final String answer;
  final bool isSelected;
  final bool isCorrect;
  final bool isIncorrect;
  final VoidCallback? onPressed;

  const AnswerButton({
    super.key,
    required this.answer,
    this.isSelected = false,
    this.isCorrect = false,
    this.isIncorrect = false,
    this.onPressed,
  });

  @override
  State<AnswerButton> createState() => _AnswerButtonState();
}

class _AnswerButtonState extends State<AnswerButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AnswerButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected && !oldWidget.isSelected) {
      _controller.forward();
    } else if (!widget.isSelected && oldWidget.isSelected) {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _getButtonColor() {
    if (widget.isCorrect) return AppTheme.correctAnswer;
    if (widget.isIncorrect) return AppTheme.incorrectAnswer;
    if (widget.isSelected) return Theme.of(context).colorScheme.primary;
    return Theme.of(context).colorScheme.surface;
  }

  Color _getTextColor() {
    if (widget.isCorrect || widget.isIncorrect || widget.isSelected) {
      return Colors.white;
    }
    return Theme.of(context).colorScheme.onSurface;
  }

  IconData? _getIcon() {
    if (widget.isCorrect) return Icons.check_circle;
    if (widget.isIncorrect) return Icons.cancel;
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: _getButtonColor().withOpacity(0.3 * _glowAnimation.value),
                  blurRadius: 8 + (4 * _glowAnimation.value),
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: _getButtonColor(),
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                onTap: widget.onPressed,
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.answer,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: _getTextColor(),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      if (_getIcon() != null) ...[
                        const SizedBox(width: 12),
                        AnimatedScale(
                          scale: widget.isCorrect || widget.isIncorrect ? 1.0 : 0.0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.elasticOut,
                          child: Icon(
                            _getIcon(),
                            color: _getTextColor(),
                            size: 24,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
