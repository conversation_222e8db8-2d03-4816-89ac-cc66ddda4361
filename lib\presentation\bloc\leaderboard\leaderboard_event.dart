import 'package:equatable/equatable.dart';

abstract class LeaderboardEvent extends Equatable {
  const LeaderboardEvent();

  @override
  List<Object?> get props => [];
}

class LoadLeaderboard extends LeaderboardEvent {
  final String? category;
  final String? difficulty;
  final int? limit;

  const LoadLeaderboard({
    this.category,
    this.difficulty,
    this.limit,
  });

  @override
  List<Object?> get props => [category, difficulty, limit];
}

class RefreshLeaderboard extends LeaderboardEvent {}

class FilterLeaderboard extends LeaderboardEvent {
  final String? category;
  final String? difficulty;

  const FilterLeaderboard({
    this.category,
    this.difficulty,
  });

  @override
  List<Object?> get props => [category, difficulty];
}
