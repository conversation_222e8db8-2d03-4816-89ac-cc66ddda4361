import 'package:equatable/equatable.dart';
import '../../../domain/entities/quiz_settings.dart';

abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadSettings extends SettingsEvent {}

class UpdateTheme extends SettingsEvent {
  final bool isDarkMode;

  const UpdateTheme(this.isDarkMode);

  @override
  List<Object?> get props => [isDarkMode];
}

class UpdateLanguage extends SettingsEvent {
  final String languageCode;

  const UpdateLanguage(this.languageCode);

  @override
  List<Object?> get props => [languageCode];
}

class UpdateSound extends SettingsEvent {
  final bool soundEnabled;

  const UpdateSound(this.soundEnabled);

  @override
  List<Object?> get props => [soundEnabled];
}

class UpdateVibration extends SettingsEvent {
  final bool vibrationEnabled;

  const UpdateVibration(this.vibrationEnabled);

  @override
  List<Object?> get props => [vibrationEnabled];
}

class UpdateNotifications extends SettingsEvent {
  final bool notificationsEnabled;

  const UpdateNotifications(this.notificationsEnabled);

  @override
  List<Object?> get props => [notificationsEnabled];
}

class UpdateAppSettings extends SettingsEvent {
  final AppSettings settings;

  const UpdateAppSettings(this.settings);

  @override
  List<Object?> get props => [settings];
}
