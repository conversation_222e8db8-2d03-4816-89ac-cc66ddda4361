// This is a basic Flutter widget test for Quiz Master app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quiz_master/main.dart';

void main() {
  testWidgets('Quiz Master app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const QuizMasterApp());

    // Verify that the app loads with the home screen
    expect(find.text('Quiz Master'), findsOneWidget);
    expect(find.text('Start Quiz'), findsOneWidget);
  });
}
