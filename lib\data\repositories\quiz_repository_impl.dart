import '../../domain/entities/category.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_result.dart';
import '../../domain/entities/quiz_settings.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../datasources/local/local_storage_service.dart';
import '../datasources/remote/quiz_api_service.dart';

class QuizRepositoryImpl implements QuizRepository {
  final QuizApiService _apiService;
  final LocalStorageService _localStorageService;

  QuizRepositoryImpl({
    required QuizApiService apiService,
    required LocalStorageService localStorageService,
  })  : _apiService = apiService,
        _localStorageService = localStorageService;

  @override
  Future<List<Category>> getCategories() async {
    try {
      final categoryModels = await _apiService.getCategories();
      return categoryModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  @override
  Future<List<Question>> getQuestions(QuizSettings settings) async {
    try {
      final questionModels = await _apiService.getQuestions(
        amount: settings.numberOfQuestions,
        category: settings.categoryId,
        difficulty: settings.difficulty,
        type: settings.type,
      );
      return questionModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to fetch questions: $e');
    }
  }

  @override
  Future<void> saveQuizResult(QuizResult result) async {
    try {
      await _localStorageService.saveQuizResult(result);
    } catch (e) {
      throw Exception('Failed to save quiz result: $e');
    }
  }

  @override
  Future<List<QuizResult>> getLeaderboard({
    String? category,
    String? difficulty,
    int? limit,
  }) async {
    try {
      return await _localStorageService.getLeaderboard(
        category: category,
        difficulty: difficulty,
        limit: limit,
      );
    } catch (e) {
      throw Exception('Failed to fetch leaderboard: $e');
    }
  }

  @override
  Future<void> clearLeaderboard() async {
    try {
      await _localStorageService.clearLeaderboard();
    } catch (e) {
      throw Exception('Failed to clear leaderboard: $e');
    }
  }
}
