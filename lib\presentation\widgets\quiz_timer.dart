import 'package:flutter/material.dart';
import 'dart:math' as math;

class QuizTimer extends StatefulWidget {
  final int timeRemaining;
  final int totalTime;
  final Color? color;

  const QuizTimer({
    super.key,
    required this.timeRemaining,
    required this.totalTime,
    this.color,
  });

  @override
  State<QuizTimer> createState() => _QuizTimerState();
}

class _QuizTimerState extends State<QuizTimer>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(QuizTimer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Start pulsing when time is running low
    if (widget.timeRemaining <= 10 && widget.timeRemaining > 0) {
      if (!_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      }
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Color _getTimerColor() {
    if (widget.color != null) return widget.color!;
    
    final percentage = widget.timeRemaining / widget.totalTime;
    if (percentage > 0.5) {
      return Colors.green;
    } else if (percentage > 0.25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    final progress = widget.timeRemaining / widget.totalTime;
    
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: _getTimerColor().withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Background Circle
                SizedBox(
                  width: 50,
                  height: 50,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: 4,
                    backgroundColor: _getTimerColor().withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getTimerColor().withOpacity(0.2),
                    ),
                  ),
                ),
                
                // Progress Circle
                SizedBox(
                  width: 50,
                  height: 50,
                  child: TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 300),
                    tween: Tween<double>(begin: 0, end: progress),
                    builder: (context, value, child) {
                      return Transform.rotate(
                        angle: -math.pi / 2,
                        child: CircularProgressIndicator(
                          value: value,
                          strokeWidth: 4,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(_getTimerColor()),
                        ),
                      );
                    },
                  ),
                ),
                
                // Time Text
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 200),
                  style: Theme.of(context).textTheme.titleSmall!.copyWith(
                    color: _getTimerColor(),
                    fontWeight: FontWeight.bold,
                    fontSize: widget.timeRemaining <= 10 ? 14 : 12,
                  ),
                  child: Text(
                    '${widget.timeRemaining}',
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
