import 'package:html_unescape/html_unescape.dart';
import '../../domain/entities/question.dart';

class QuestionModel extends Question {
  const QuestionModel({
    required super.category,
    required super.type,
    required super.difficulty,
    required super.question,
    required super.correctAnswer,
    required super.incorrectAnswers,
    required super.allAnswers,
  });

  factory QuestionModel.fromJson(Map<String, dynamic> json) {
    final unescape = HtmlUnescape();
    
    final correctAnswer = unescape.convert(json['correct_answer'] ?? '');
    final incorrectAnswers = (json['incorrect_answers'] as List<dynamic>?)
            ?.map((answer) => unescape.convert(answer.toString()))
            .toList() ??
        [];

    // Shuffle all answers
    final allAnswers = [correctAnswer, ...incorrectAnswers];
    allAnswers.shuffle();

    return QuestionModel(
      category: unescape.convert(json['category'] ?? ''),
      type: json['type'] ?? 'multiple',
      difficulty: json['difficulty'] ?? 'medium',
      question: unescape.convert(json['question'] ?? ''),
      correctAnswer: correctAnswer,
      incorrectAnswers: incorrectAnswers,
      allAnswers: allAnswers,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'type': type,
      'difficulty': difficulty,
      'question': question,
      'correct_answer': correctAnswer,
      'incorrect_answers': incorrectAnswers,
    };
  }

  factory QuestionModel.fromEntity(Question question) {
    return QuestionModel(
      category: question.category,
      type: question.type,
      difficulty: question.difficulty,
      question: question.question,
      correctAnswer: question.correctAnswer,
      incorrectAnswers: question.incorrectAnswers,
      allAnswers: question.allAnswers,
    );
  }

  Question toEntity() {
    return Question(
      category: category,
      type: type,
      difficulty: difficulty,
      question: question,
      correctAnswer: correctAnswer,
      incorrectAnswers: incorrectAnswers,
      allAnswers: allAnswers,
    );
  }
}
