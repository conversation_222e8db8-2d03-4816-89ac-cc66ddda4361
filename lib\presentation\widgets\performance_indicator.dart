import 'package:flutter/material.dart';

class PerformanceIndicator extends StatefulWidget {
  final double percentage;
  final String performanceLevel;

  const PerformanceIndicator({
    super.key,
    required this.percentage,
    required this.performanceLevel,
  });

  @override
  State<PerformanceIndicator> createState() => _PerformanceIndicatorState();
}

class _PerformanceIndicatorState extends State<PerformanceIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _getPerformanceColor() {
    if (widget.percentage >= 90) return Colors.amber;
    if (widget.percentage >= 70) return Colors.green;
    if (widget.percentage >= 50) return Colors.orange;
    return Colors.red;
  }

  IconData _getPerformanceIcon() {
    if (widget.percentage >= 90) return Icons.emoji_events;
    if (widget.percentage >= 70) return Icons.thumb_up;
    if (widget.percentage >= 50) return Icons.sentiment_satisfied;
    return Icons.sentiment_dissatisfied;
  }

  List<Widget> _buildStars() {
    final starCount = _getStarCount();
    return List.generate(5, (index) {
      return AnimatedContainer(
        duration: Duration(milliseconds: 200 + (index * 100)),
        child: Icon(
          index < starCount ? Icons.star : Icons.star_border,
          color: index < starCount ? Colors.amber : Colors.grey,
          size: 24,
        ),
      );
    });
  }

  int _getStarCount() {
    if (widget.percentage >= 90) return 5;
    if (widget.percentage >= 80) return 4;
    if (widget.percentage >= 60) return 3;
    if (widget.percentage >= 40) return 2;
    if (widget.percentage >= 20) return 1;
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: _getPerformanceColor().withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: _getPerformanceColor().withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            // Performance Icon
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getPerformanceColor().withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getPerformanceIcon(),
                size: 32,
                color: _getPerformanceColor(),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Performance Level Text
            Text(
              widget.performanceLevel,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: _getPerformanceColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Star Rating
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _buildStars(),
            ),
            
            const SizedBox(height: 8),
            
            // Performance Description
            Text(
              _getPerformanceDescription(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getPerformanceDescription() {
    if (widget.percentage >= 90) {
      return 'Outstanding! You\'re a true quiz master!';
    } else if (widget.percentage >= 70) {
      return 'Great job! You really know your stuff!';
    } else if (widget.percentage >= 50) {
      return 'Good work! Keep practicing to improve!';
    } else {
      return 'Don\'t give up! Practice makes perfect!';
    }
  }
}
